name: Run Tests

on:
  push:
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ["3.11"] # Matches pyproject.toml

    steps:
    - name: Checkout code
      uses: actions/checkout@v4
      with:
        submodules: 'recursive' # As per AGENTS.md, vendor/llama.cpp is a submodule

    - name: Set up Python ${{ matrix.python-version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ matrix.python-version }}

    - name: Install Poetry
      run: |
        pipx install poetry

    - name: Install dependencies
      run: |
        poetry install

    - name: Run tests
      run: |
        poetry run pytest -v
