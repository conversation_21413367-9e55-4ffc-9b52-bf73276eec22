import json
from dataclasses import dataclass, asdict
from typing import List, Optional

@dataclass
class GpuInfo:
    model_name: str
    vram_gb: float

@dataclass
class SystemProfile:
    cpu_cores: int
    total_ram_gb: float
    gpus: List[GpuInfo]
    numa_detected: bool
    blas_backend: str

@dataclass
class ModelProfile:
    file_path: str
    architecture: str
    layer_count: int
    quantization_type: str

@dataclass
class BenchmarkResult:
    n_gpu_layers: int
    prompt_speed_tps: float
    generation_speed_tps: float
    batch_size: Optional[int]
    parallel_level: Optional[int]

@dataclass
class OptimalConfiguration:
    system_profile: SystemProfile
    model_profile: ModelProfile
    best_benchmark_result: BenchmarkResult
    generated_command: str
    notes: List[str]

    def to_dict(self):
        """Converts the OptimalConfiguration object to a dictionary."""
        return asdict(self)