import typer
import logging
from typing_extensions import Annotated
from typing import Optional
from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn, TimeRemainingColumn
from rich.console import Console
from llama_tune.analyzer.analyzer import get_system_profile, get_model_profile, run_feasibility_check, FeasibilityError
from llama_tune.core.data_models import OptimalConfiguration, BenchmarkResult, ModelProfile
from llama_tune.reporting.output_generator import generate_output
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine, ProgressCallback

app = typer.Typer()
logger = logging.getLogger(__name__)

@app.command()
def tune(
    model_path: Annotated[Optional[str], typer.Option("--model-path", "-m", help="Path to the GGUF model file.")] = None,
    verbose: Annotated[bool, typer.Option(help="Enable verbose output.")] = False,
    json_output: Annotated[bool, typer.Option(help="Output results as JSON.")] = False,
    interactive: Annotated[bool, typer.Option("--interactive", help="Launch an interactive setup wizard.")] = False,
    benchmark: Annotated[bool, typer.Option("--benchmark", help="Initiate automated benchmarking.")] = False,
    ctx_size: Annotated[Optional[int], typer.Option("--ctx-size", help="Target context size for benchmarking.")] = None,
):
    """
    Analyzes system hardware and GGUF model to recommend optimal llama.cpp settings.
    """
    logging.basicConfig(level=logging.INFO if verbose else logging.WARNING)

    if interactive:
        from llama_tune.wizard.interactive_wizard import InteractiveWizard
        wizard = InteractiveWizard()
        wizard.start()
        
        optimal_config = wizard.get_optimal_configuration()
        if optimal_config:
            output_mode = "json" if json_output else ("verbose" if verbose else "default")
            final_output = generate_output(optimal_config, output_mode)
            
            typer.echo("\n" + final_output) # Add a newline for separation
            typer.echo("Configuration complete!")
        else:
            typer.echo("Wizard did not complete successfully. No command generated.")
        raise typer.Exit()


    if benchmark:
        if model_path is None:
            typer.echo("Error: --model-path is required when using --benchmark.")
            raise typer.Exit(code=1)
        if ctx_size is None:
            typer.echo("Error: --ctx-size is required when using --benchmark.")
            raise typer.Exit(code=1)
        
        # Task 3: Call BenchmarkingEngine.run_benchmark()
        benchmarking_engine = BenchmarkingEngine()
        
        console = Console()
        
        with Progress(
            SpinnerColumn(),
            TextColumn("[progress.description]{task.description}"),
            BarColumn(),
            TextColumn("[progress.percentage]{task.percentage:>3.0f}%"),
            TimeElapsedColumn(),
            TimeRemainingColumn(),
            TextColumn("[bold blue]{task.fields.get('speed', '')}"),
            console=console,
            transient=True,
        ) as progress:
            overall_task = progress.add_task("[green]Overall Benchmark Progress", total=100, speed="")

            def update_progress_callback(phase_name: str, step_description: str, current_step: int, total_steps: int, speed: Optional[float] = None):
                progress_percentage = (current_step / total_steps) * 100 if total_steps > 0 else 0
                progress.update(overall_task, description=f"[green]{phase_name}: [cyan]{step_description}", completed=progress_percentage, speed=f"{speed:.2f} t/s" if speed is not None else "")
                
            optimal_config = benchmarking_engine.run_benchmark(model_path, ctx_size, "default", update_progress_callback)

        output_mode = "json" if json_output else ("verbose" if verbose else "default")
        final_output = generate_output(optimal_config, output_mode)
        
        typer.echo("\n" + final_output) # Add a newline for separation
        typer.echo("Benchmarking complete!")
        raise typer.Exit()

    if model_path is None:
        typer.echo("Error: --model-path is required when not running in interactive or benchmark mode.")
        raise typer.Exit(code=1)

    logger.info("Starting llama-tune analysis...")

    # 1. Detect System Profile
    system_profile = get_system_profile()
    logger.info(f"Detected System Profile: {system_profile}")

    # 2. Get Model Profile
    try:
        model_profile = get_model_profile(model_path)
        logger.info(f"Detected Model Profile: {model_profile}")
    except FeasibilityError as e:
        typer.echo(f"Error: {e}")
        raise typer.Exit(code=1)

    # 3. Perform Pre-flight Feasibility Check
    try:
        run_feasibility_check(model_profile, system_profile)
    except FeasibilityError as e:
        typer.echo(f"Error: {e}")
        raise typer.Exit(code=1)

    # The logic for generating optimal_config for non-interactive mode
    # will remain here for now, but will be refactored in future stories
    # when benchmarking is implemented.
    dummy_benchmark_result = BenchmarkResult(
        n_gpu_layers=0,
        prompt_speed_tps=0.0,
        generation_speed_tps=0.0,
        batch_size=None,
        parallel_level=None
    )

    # For non-interactive mode, we create the configuration with default values.
    # The OutputGenerator will build the complete command from this data.
    notes = []
    if system_profile.cpu_cores == 0:
        notes.append("Could not detect physical CPU cores. The --threads argument was omitted from the command.")

    optimal_config = OptimalConfiguration(
        system_profile=system_profile,
        model_profile=model_profile,
        best_benchmark_result=dummy_benchmark_result,
        generated_command="",  # Empty - OutputGenerator will build the complete command
        notes=notes,
        ctx_size=2048,  # Default context size for non-interactive
        sampling_parameters={}  # No sampling parameters in non-interactive mode
    )
    
    # For non-interactive mode, we don't need a progress bar, just print the final output

    output_mode = "json" if json_output else ("verbose" if verbose else "default")
    final_output = generate_output(optimal_config, output_mode)
    
    typer.echo(final_output)

if __name__ == "__main__":
    app()