import typer
import logging
from typing_extensions import Annotated
from typing import Optional
from llama_tune.analyzer.analyzer import get_system_profile, get_model_profile, run_feasibility_check, FeasibilityError
from llama_tune.core.data_models import OptimalConfiguration, BenchmarkResult, ModelProfile
from llama_tune.reporting.output_generator import generate_output

app = typer.Typer()
logger = logging.getLogger(__name__)

@app.command()
def tune(
    model_path: Annotated[Optional[str], typer.Option("--model-path", "-m", help="Path to the GGUF model file.")] = None,
    verbose: Annotated[bool, typer.Option(help="Enable verbose output.")] = False,
    json_output: Annotated[bool, typer.Option(help="Output results as JSON.")] = False,
    interactive: Annotated[bool, typer.Option("--interactive", help="Launch an interactive setup wizard.")] = False,
):
    """
    Analyzes system hardware and GGUF model to recommend optimal llama.cpp settings.
    """
    logging.basicConfig(level=logging.INFO if verbose else logging.WARNING)

    if interactive:
        from llama_tune.wizard.interactive_wizard import InteractiveWizard
        wizard = InteractiveWizard()
        wizard.start()
        
        optimal_config = wizard.get_optimal_configuration()
        if optimal_config:
            output_mode = "json" if json_output else ("verbose" if verbose else "default")
            final_output = generate_output(optimal_config, output_mode)
            
            typer.echo("\n" + final_output) # Add a newline for separation
            typer.echo("Configuration complete!")
        else:
            typer.echo("Wizard did not complete successfully. No command generated.")
        raise typer.Exit()

    if model_path is None:
        typer.echo("Error: --model-path is required when not running in interactive mode.")
        raise typer.Exit(code=1)

    logger.info("Starting llama-tune analysis...")

    # 1. Detect System Profile
    system_profile = get_system_profile()
    logger.info(f"Detected System Profile: {system_profile}")

    # 2. Get Model Profile
    try:
        model_profile = get_model_profile(model_path)
        logger.info(f"Detected Model Profile: {model_profile}")
    except FeasibilityError as e:
        typer.echo(f"Error: {e}")
        raise typer.Exit(code=1)

    # 3. Perform Pre-flight Feasibility Check
    try:
        run_feasibility_check(model_profile, system_profile)
    except FeasibilityError as e:
        typer.echo(f"Error: {e}")
        raise typer.Exit(code=1)

    # The logic for generating optimal_config for non-interactive mode
    # will remain here for now, but will be refactored in future stories
    # when benchmarking is implemented.
    dummy_benchmark_result = BenchmarkResult(
        n_gpu_layers=0,
        prompt_speed_tps=0.0,
        generation_speed_tps=0.0,
        batch_size=None,
        parallel_level=None
    )

    # For non-interactive mode, we construct a basic command.
    # The full command generation logic (including mlock, numa, blas)
    # is now primarily handled by OutputGenerator.
    base_command_parts = ["./llama.cpp/main"]
    if system_profile.cpu_cores > 0:
        base_command_parts.append(f"--threads {system_profile.cpu_cores}")
    base_command_parts.append(f"--model {model_path}")
    base_command_parts.append("--ctx-size 2048") # Default context size for non-interactive
    base_command_parts.append("--n-gpu-layers 0") # Default for now

    generated_command = " ".join(base_command_parts)

    notes = []
    if system_profile.cpu_cores == 0:
        notes.append("Could not detect physical CPU cores. The --threads argument was omitted from the command.")

    optimal_config = OptimalConfiguration(
        system_profile=system_profile,
        model_profile=model_profile,
        best_benchmark_result=dummy_benchmark_result,
        generated_command=generated_command, # This will be further enhanced by OutputGenerator
        notes=notes
    )

    output_mode = "json" if json_output else ("verbose" if verbose else "default")
    final_output = generate_output(optimal_config, output_mode)
    
    typer.echo(final_output)

if __name__ == "__main__":
    app()