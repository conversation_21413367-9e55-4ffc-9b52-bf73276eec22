import json
import os # Added for os.path.getsize
from llama_tune.core.data_models import OptimalConfiguration, ModelProfile, SystemProfile
from llama_tune.analyzer.analyzer import should_recommend_mlock

def build_command_from_config(config: OptimalConfiguration) -> str:
    """
    Builds the complete llama.cpp command from OptimalConfiguration data.
    This is the single source of truth for command generation.
    """
    command_parts = ["./llama.cpp/main"]

    # Add threads if CPU cores detected
    if config.system_profile.cpu_cores > 0:
        command_parts.append(f"--threads {config.system_profile.cpu_cores}")

    # Add model path
    command_parts.append(f"--model {config.model_profile.file_path}")

    # Add context size if specified
    if config.ctx_size is not None:
        command_parts.append(f"--ctx-size {config.ctx_size}")

    # Add sampling parameters if specified
    if config.sampling_parameters:
        for param, value in config.sampling_parameters.items():
            if value is not None:
                command_parts.append(f"--{param} {value}")

    # Add GPU layers from benchmark result
    command_parts.append(f"--n-gpu-layers {config.best_benchmark_result.n_gpu_layers}")

    # Add NUMA flag if detected
    if config.system_profile.numa_detected:
        command_parts.append("--numa distribute")

    # Add mlock if recommended
    recommend_mlock = should_recommend_mlock(config.model_profile, config.system_profile)
    if recommend_mlock:
        command_parts.append("--mlock")

    # Add BLAS backend flag if detected
    if config.system_profile.blas_backend == "cuBLAS":
        command_parts.append("--use-cublas")
    elif config.system_profile.blas_backend == "CLBlast":
        command_parts.append("--use-clblast")

    return " ".join(command_parts)

def generate_output(config: OptimalConfiguration, mode: str) -> str:
    """
    Generates the final output string based on the OptimalConfiguration and output mode.
    """
    if mode == "json":
        # Placeholder for JSON output
        return json.dumps(config.to_dict(), indent=2)
    elif mode == "verbose":
        output = f"System Profile:\n"
        output += f"  CPU Cores: {config.system_profile.cpu_cores}\n"
        if config.system_profile.cpu_cores == 0:
            output += "  Warning: Could not detect physical CPU cores. --threads argument will be omitted.\n"
        output += f"  Total RAM (GB): {config.system_profile.total_ram_gb}\n"
        output += f"  GPUs: {len(config.system_profile.gpus)}\n"
        for gpu in config.system_profile.gpus:
            output += f"    - {gpu.model_name} ({gpu.vram_gb} GB VRAM)\n"
        output += f"  NUMA Detected: {config.system_profile.numa_detected}\n"
        output += f"  BLAS Backend: {config.system_profile.blas_backend}\n\n"

        output += f"Model Profile:\n"
        output += f"  File Path: {config.model_profile.file_path}\n"
        output += f"  Architecture: {config.model_profile.architecture}\n"
        output += f"  Layer Count: {config.model_profile.layer_count}\n"
        output += f"  Quantization Type: {config.model_profile.quantization_type}\n\n"

        output += f"Best Benchmark Result:\n"
        output += f"  GPU Layers: {config.best_benchmark_result.n_gpu_layers}\n"
        output += f"  Prompt Speed (tps): {config.best_benchmark_result.prompt_speed_tps}\n"
        output += f"  Generation Speed (tps): {config.best_benchmark_result.generation_speed_tps}\n"
        output += f"  Batch Size: {config.best_benchmark_result.batch_size}\n"
        output += f"  Parallel Level: {config.best_benchmark_result.parallel_level}\n\n"

        # Build the complete command from configuration data
        final_command = build_command_from_config(config)

        # Add explanatory notes for verbose output
        if config.system_profile.numa_detected:
            config.notes.append("NUMA architecture detected. '--numa distribute' recommended for optimal performance.")

        # Determine if --mlock should be recommended and add note
        recommend_mlock = should_recommend_mlock(config.model_profile, config.system_profile)
        if recommend_mlock:
            config.notes.append("Model size is comfortably within available RAM. '--mlock' recommended to lock model into RAM for consistent performance.")
        else:
            # Only add a note if mlock was considered but omitted
            # This avoids cluttering output if mlock is not applicable (e.g., very small models)
            try:
                import os
                model_file_size_bytes = os.path.getsize(config.model_profile.file_path)
                model_file_size_gb = round(model_file_size_bytes / (1024**3), 2)
                # Check if the model size is significant enough to warrant an explanation for omitting mlock
                # For example, if model is > 1GB, explain why mlock is not used.
                if model_file_size_gb > 1.0:
                    config.notes.append("Model size is too large to comfortably fit into available RAM with safety margin. '--mlock' omitted to prevent system instability.")
            except Exception:
                # If file size cannot be determined, skip the detailed omission note
                pass

        # Add BLAS backend notes
        if config.system_profile.blas_backend == "cuBLAS":
            config.notes.append("cuBLAS backend detected. '--use-cublas' added for GPU acceleration.")
        elif config.system_profile.blas_backend == "OpenBLAS":
            # OpenBLAS is often enabled by default or through environment variables,
            # but some llama.cpp builds might have a specific flag or it might be implied.
            # For now, we'll just add a note if it's detected.
            config.notes.append("OpenBLAS backend detected. Ensure llama.cpp is compiled with OpenBLAS for CPU performance.")
        elif config.system_profile.blas_backend == "BLIS":
            config.notes.append("BLIS backend detected. Ensure llama.cpp is compiled with BLIS for CPU performance.")
        elif config.system_profile.blas_backend == "CLBlast":
            config.notes.append("CLBlast backend detected. '--use-clblast' added for OpenCL GPU acceleration.")
        elif config.system_profile.blas_backend == "None":
            config.notes.append("No high-performance BLAS backend detected. Performance may be limited to default CPU implementation.")
        else:
            config.notes.append(f"Unknown BLAS backend '{config.system_profile.blas_backend}' detected. No specific flag added.")
        output += f"Generated Command: {final_command}\n\n"

        if config.notes:
            output += "Notes:\n"
            for note in config.notes:
                output += f"- {note}\n"
        return output
    else:
        # Default output (complete command without verbose explanations)
        return build_command_from_config(config)