import logging
from typing import List
import psutil
import subprocess
import platform
import re
import os

from llama_tune.core.data_models import SystemProfile, GpuInfo, ModelProfile
from llama_tune.analyzer.cpu_analyzer import get_physical_cpu_cores # Re-added this import

# Custom exception for model parsing errors
class ModelParsingError(Exception):
    """Custom exception for errors encountered during GGUF model parsing."""
    pass

class FeasibilityError(Exception):
    """Custom exception for pre-flight feasibility check failures."""
    pass

logger = logging.getLogger(__name__)

def get_total_ram_gb() -> float:
    """
    Detects the total amount of system RAM in Gigabytes (GB).
    Returns 0.0 if RAM cannot be determined.
    """
    try:
        total_bytes = psutil.virtual_memory().total
        total_gb = round(total_bytes / (1024**3), 2)
        return total_gb
    except Exception as e:
        logger.warning(f"Could not detect total RAM: {e}")
        return 0.0

def get_system_profile() -> SystemProfile:
    """
    Detects system hardware specifications and returns a SystemProfile object.
    """
    cpu_cores = get_physical_cpu_cores()
    total_ram_gb = get_total_ram_gb()
    
    gpus: List[GpuInfo] = get_gpu_info()
    numa_detected = is_numa_architecture()
    blas_backend = get_blas_backend()

    if cpu_cores == 0:
        logger.warning("Could not detect physical CPU cores. --threads argument will be omitted.")
    
    if total_ram_gb == 0.0:
        logger.warning("Could not detect total RAM. Memory-dependent checks will be skipped.")

    return SystemProfile(
        cpu_cores=cpu_cores,
        total_ram_gb=total_ram_gb,
        gpus=gpus,
        numa_detected=numa_detected,
        blas_backend=blas_backend
    )

def get_blas_backend() -> str:
    """
    Detects the BLAS backend used by llama.cpp by running llama-bench and parsing its output.
    Returns a string indicating the detected backend (e.g., "cuBLAS", "OpenBLAS", "BLIS", "CLBlast", "None").
    """
    logger.info("Attempting to detect BLAS backend...")
    command = ["llama-bench", "-p", "1", "-n", "1", "--quiet"] # Minimal command to get startup output
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=False, timeout=10)
        output = result.stdout + result.stderr # Check both stdout and stderr for build info

        # Keywords for different BLAS backends
        if re.search(r"BLAS = 1 \(cuBLAS\)", output, re.IGNORECASE) or re.search(r"CUDA_BLAS", output, re.IGNORECASE):
            logger.info("Detected cuBLAS backend.")
            return "cuBLAS"
        elif re.search(r"BLAS = 1 \(OpenBLAS\)", output, re.IGNORECASE) or re.search(r"OPENBLAS", output, re.IGNORECASE):
            logger.info("Detected OpenBLAS backend.")
            return "OpenBLAS"
        elif re.search(r"BLAS = 1 \(BLIS\)", output, re.IGNORECASE) or re.search(r"BLIS", output, re.IGNORECASE):
            logger.info("Detected BLIS backend.")
            return "BLIS"
        elif re.search(r"BLAS = 1 \(CLBlast\)", output, re.IGNORECASE) or re.search(r"CLBlast", output, re.IGNORECASE):
            logger.info("Detected CLBlast backend.")
            return "CLBlast"
        else:
            logger.info("No specific high-performance BLAS backend detected or recognized. Assuming default/None.")
            return "None"
    except FileNotFoundError:
        logger.warning("llama-bench executable not found. Cannot detect BLAS backend. Assuming 'None'.")
        return "None"
    except subprocess.TimeoutExpired:
        logger.warning("llama-bench command timed out. Cannot detect BLAS backend. Assuming 'None'.")
        return "None"
    except Exception as e:
        logger.warning(f"An error occurred during BLAS backend detection: {e}. Assuming 'None'.")
        return "None"

def _detect_nvidia_gpu() -> List[GpuInfo]:
    """Detects NVIDIA GPUs and their VRAM using nvidia-smi."""
    gpus = []
    try:
        # Use 'nvidia-smi --query-gpu=name,memory.total --format=csv,noheader,nounits'
        # to get GPU name and total memory in MB.
        # Example output:
        # NVIDIA GeForce RTX 4070, 12282 MiB
        # NVIDIA GeForce RTX 3060, 12288 MiB
        command = ["nvidia-smi", "--query-gpu=name,memory.total", "--format=csv,noheader,nounits"]
        result = subprocess.run(command, capture_output=True, text=True, check=True, timeout=10)
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = line.split(',')
                if len(parts) == 2:
                    model_name = parts[0].strip()
                    # nvidia-smi reports in MiB, convert to GB
                    vram_mib = float(parts[1].strip().replace('MiB', '').strip())
                    vram_gb = round(vram_mib / 1024, 2)
                    gpus.append(GpuInfo(model_name=model_name, vram_gb=vram_gb))
    except (subprocess.CalledProcessError, FileNotFoundError, ValueError, subprocess.TimeoutExpired) as e:
        logger.debug(f"NVIDIA GPU detection failed or nvidia-smi not found: {e}")
    return gpus

def _detect_apple_metal_gpu() -> List[GpuInfo]:
    """Detects Apple Silicon (Metal) GPUs and their VRAM on macOS."""
    gpus = []
    if platform.system() == "Darwin":
        try:
            # Use system_profiler to get GPU information on macOS
            # Example output (simplified):
            # Graphics/Displays:
            #
            #     Apple M1:
            #       VRAM (Total): 8 GB
            #       Chipset Model: Apple M1
            command = ["system_profiler", "SPDisplaysDataType"]
            result = subprocess.run(command, capture_output=True, text=True, check=True, timeout=10)
            output = result.stdout

            # Regex to find GPU name and VRAM
            gpu_pattern = re.compile(r'^\s*(.+?):\s*$', re.MULTILINE)
            vram_pattern = re.compile(r'VRAM \(Total\): (\d+\.?\d*)\s*GB', re.MULTILINE)

            current_gpu_name = None
            for line in output.split('\n'):
                gpu_match = gpu_pattern.match(line)
                if gpu_match:
                    current_gpu_name = gpu_match.group(1).strip()
                
                vram_match = vram_pattern.search(line)
                if vram_match and current_gpu_name:
                    vram_gb = float(vram_match.group(1))
                    gpus.append(GpuInfo(model_name=current_gpu_name, vram_gb=vram_gb))
                    current_gpu_name = None # Reset for next GPU
        except (subprocess.CalledProcessError, FileNotFoundError, ValueError, subprocess.TimeoutExpired) as e:
            logger.debug(f"Apple Metal GPU detection failed or system_profiler not found: {e}")
    return gpus

def _detect_amd_gpu() -> List[GpuInfo]:
    """Detects AMD GPUs and their VRAM using rocm-smi."""
    gpus = []
    try:
        # Use 'rocm-smi --showproductname --showmeminfo vram --csv'
        # Example output:
        # card,product_name,vram_total_mb
        # 0,AMD Radeon RX 6800 XT,16384
        command = ["rocm-smi", "--showproductname", "--showmeminfo", "vram", "--csv"]
        result = subprocess.run(command, capture_output=True, text=True, check=True, timeout=10)
        lines = result.stdout.strip().split('\n')
        if len(lines) > 1: # Skip header
            for line in lines[1:]:
                if line.strip():
                    parts = line.split(',')
                    if len(parts) >= 3:
                        model_name = parts[1].strip()
                        # rocm-smi reports in MB, convert to GB
                        vram_mb = float(parts[2].strip())
                        vram_gb = round(vram_mb / 1024, 2)
                        gpus.append(GpuInfo(model_name=model_name, vram_gb=vram_gb))
    except (subprocess.CalledProcessError, FileNotFoundError, ValueError, subprocess.TimeoutExpired) as e:
        logger.debug(f"AMD GPU detection failed or rocm-smi not found: {e}")
    return gpus

def get_gpu_info() -> List[GpuInfo]:
    """
    Detects available GPUs and their VRAM capacity.
    Attempts detection for NVIDIA, Apple Metal, and AMD GPUs.
    Returns an empty list if no compatible GPUs are found or tools are not installed.
    """
    detected_gpus: List[GpuInfo] = []

    # Try NVIDIA
    nvidia_gpus = _detect_nvidia_gpu()
    if nvidia_gpus:
        detected_gpus.extend(nvidia_gpus)
        logger.info(f"Detected NVIDIA GPUs: {[gpu.model_name for gpu in nvidia_gpus]}")

    # Try Apple Metal (macOS only)
    if platform.system() == "Darwin":
        apple_gpus = _detect_apple_metal_gpu()
        if apple_gpus:
            detected_gpus.extend(apple_gpus)
            logger.info(f"Detected Apple Metal GPUs: {[gpu.model_name for gpu in apple_gpus]}")

    # Try AMD
    amd_gpus = _detect_amd_gpu()
    if amd_gpus:
        detected_gpus.extend(amd_gpus)
        logger.info(f"Detected AMD GPUs: {[gpu.model_name for gpu in amd_gpus]}")

    if not detected_gpus:
        logger.info("No compatible GPUs detected or necessary tools not found.")
    
    return detected_gpus

def is_numa_architecture() -> bool:
    """
    Detects if the system has a NUMA (Non-Uniform Memory Access) architecture.
    Primarily checks on Linux systems by looking for /sys/devices/system/node.
    Returns False for Windows, macOS, or if NUMA detection fails.
    """
    if platform.system() == "Linux":
        # Check for the existence of NUMA nodes in sysfs
        # A typical NUMA system will have directories like /sys/devices/system/node/node0, node1, etc.
        numa_node_path = "/sys/devices/system/node"
        if os.path.exists(numa_node_path) and os.path.isdir(numa_node_path):
            # Check if there's more than one NUMA node, or if node0 exists (basic check)
            # A single node system might still expose /sys/devices/system/node/node0
            # but true NUMA benefits come from multiple nodes.
            # For simplicity, we'll consider it NUMA if node0 exists and there are other nodes or just node0.
            # The presence of 'node0' usually indicates NUMA support in the kernel.
            node_dirs = [d for d in os.listdir(numa_node_path) if d.startswith('node') and os.path.isdir(os.path.join(numa_node_path, d))]
            if len(node_dirs) > 1:
                logger.info(f"Detected {len(node_dirs)} NUMA nodes.")
                return True
            elif 'node0' in node_dirs:
                # Even if only node0 is present, it indicates NUMA architecture is exposed.
                # This might be a single-node NUMA system or a system where NUMA is enabled but only one node is active.
                logger.info("Detected NUMA architecture with a single node (node0).")
                return True
            else:
                logger.info("NUMA node directories not found or empty in /sys/devices/system/node.")
                return False
        else:
            logger.info(f"NUMA system path '{numa_node_path}' does not exist or is not a directory.")
            return False
    elif platform.system() in ["Windows", "Darwin"]:
        logger.info(f"NUMA detection not supported or less common on {platform.system()}. Returning False.")
        return False
    else:
        logger.info(f"Unsupported operating system for NUMA detection: {platform.system()}. Returning False.")
        return False

def get_model_profile(path: str) -> ModelProfile:
    """
    Parses GGUF model metadata using the llama-gguf executable and returns a ModelProfile object.

    Args:
        path (str): The file path to the GGUF model.

    Returns:
        ModelProfile: An object containing the extracted model metadata.

    Raises:
        ModelParsingError: If the llama-gguf command fails, the file is not a valid GGUF model,
                           or required metadata cannot be extracted.
    """
    logger.info(f"Attempting to parse model profile for: {path}")

    if not os.path.exists(path):
        raise ModelParsingError(f"Model file not found at: {path}")
    if not os.path.isfile(path):
        raise ModelParsingError(f"Path is not a file: {path}")

    command = ["llama-gguf", path]
    try:
        result = subprocess.run(command, capture_output=True, text=True, check=True, timeout=60)
        output = result.stdout
        
        architecture_match = re.search(r"model: arch\s*:\s*(\w+)", output)
        layer_count_match = re.search(r"model: n_layer\s*:\s*(\d+)", output)
        quantization_type_match = re.search(r"model: ftype\s*:\s*(\w+)", output) # Corrected regex

        architecture = architecture_match.group(1).strip() if architecture_match else None
        layer_count = int(layer_count_match.group(1).strip()) if layer_count_match else None
        quantization_type = quantization_type_match.group(1).strip() if quantization_type_match else None

        if not all([architecture, layer_count, quantization_type]):
            missing_fields = []
            if not architecture: missing_fields.append("architecture")
            if not layer_count: missing_fields.append("layer_count")
            if not quantization_type: missing_fields.append("quantization_type")
            raise ModelParsingError(
                f"Failed to extract all required metadata from GGUF model '{path}'. "
                f"Missing fields: {', '.join(missing_fields)}. "
                f"llama-gguf output: \n{output}"
            )
        
        # Assert that these are not None for type checkers, as the check above ensures they are present
        assert architecture is not None
        assert layer_count is not None
        assert quantization_type is not None

        logger.info(f"Successfully parsed model profile for {path}: "
                    f"Architecture={architecture}, Layers={layer_count}, Quantization={quantization_type}")
        
        return ModelProfile(
            file_path=path,
            architecture=architecture,
            layer_count=layer_count,
            quantization_type=quantization_type
        )

    except FileNotFoundError:
        raise ModelParsingError(
            "The 'llama-gguf' executable was not found. "
            "Please ensure it is installed and available in your system's PATH."
        )
    except subprocess.CalledProcessError as e:
        error_message = e.stderr.strip()
        if "ERROR: failed to open" in error_message or "ERROR: invalid file" in error_message:
            raise ModelParsingError(f"The provided file '{path}' is not a valid GGUF model. Error: {error_message}")
        else:
            raise ModelParsingError(f"llama-gguf command failed for '{path}'. Error: {error_message}")
    except subprocess.TimeoutExpired:
        raise ModelParsingError(f"llama-gguf command timed out for '{path}'.")
    except Exception as e:
        raise ModelParsingError(f"An unexpected error occurred while parsing model '{path}': {e}")

def estimate_memory_footprint(model: ModelProfile) -> tuple[float, float]:
    """
    Estimates the model's required RAM and VRAM footprint in GB based on its size and quantization type.
    The estimation is conservative to prevent OOM errors.

    Args:
        model (ModelProfile): The profile of the GGUF model.

    Returns:
        tuple[float, float]: A tuple containing (estimated_ram_gb, estimated_vram_gb).
    """
    # Get model file size in GB
    try:
        model_file_size_bytes = os.path.getsize(model.file_path)
        model_file_size_gb = round(model_file_size_bytes / (1024**3), 2)
    except FileNotFoundError:
        logger.warning(f"Model file not found for size estimation: {model.file_path}. Assuming 0 GB.")
        model_file_size_gb = 0.0
    except Exception as e:
        logger.warning(f"Could not get model file size for {model.file_path}: {e}. Assuming 0 GB.")
        model_file_size_gb = 0.0

    # Quantization type multipliers (conservative estimates)
    # These are rough estimates. Actual memory usage can vary.
    # Assuming a base of 1.0 for Q8_0 (8-bit) and scaling down.
    # Q4_0, Q4_K_M are typically 4-bit, Q5_K_M is 5-bit.
    quantization_multiplier = {
        "Q4_0": 0.5,
        "Q4_K_M": 0.5,
        "Q5_K_M": 0.625,
        "Q8_0": 1.0,
        "F16": 2.0, # Full precision 16-bit
        "F32": 4.0, # Full precision 32-bit
    }.get(model.quantization_type.upper(), 1.0) # Default to 1.0 (Q8_0 equivalent) if unknown

    # Estimated VRAM is primarily the model size, potentially slightly more for overhead
    # We'll use the file size directly as a conservative estimate for VRAM.
    estimated_vram_gb = model_file_size_gb * quantization_multiplier

    # Estimated RAM is typically higher than VRAM due to KV cache, context, etc.
    # A common rule of thumb is 1.5x to 2x the model's base size for RAM.
    # We'll use 1.5x the original model file size as a conservative estimate for RAM.
    estimated_ram_gb = model_file_size_gb * 1.5

    logger.info(f"Estimated memory footprint for model '{os.path.basename(model.file_path)}' ({model.quantization_type}):")
    logger.info(f"  Estimated RAM: {estimated_ram_gb:.2f} GB")
    logger.info(f"  Estimated VRAM: {estimated_vram_gb:.2f} GB")

    return estimated_ram_gb, estimated_vram_gb

def run_feasibility_check(model: ModelProfile, system: SystemProfile):
    """
    Performs a pre-flight feasibility check to determine if the model is likely to fit
    into the system's RAM and VRAM, accounting for system overhead.

    Args:
        model (ModelProfile): The profile of the GGUF model.
        system (SystemProfile): The detected system hardware profile.

    Raises:
        FeasibilityError: If the model is unlikely to fit in system RAM or VRAM.
    """
    logger.info("Performing pre-flight feasibility check...")

    estimated_ram_gb, estimated_vram_gb = estimate_memory_footprint(model)

    # Account for system overhead (e.g., OS, other applications)
    # Using a conservative 4 GB for RAM overhead. VRAM overhead is usually less critical
    # for just loading the model, but we'll consider it for the total available.
    RAM_OVERHEAD_GB = 4.0
    VRAM_OVERHEAD_PER_GPU_GB = 0.5 # Smaller overhead for VRAM

    available_ram_for_model = system.total_ram_gb - RAM_OVERHEAD_GB
    
    total_available_vram = sum(gpu.vram_gb for gpu in system.gpus)
    available_vram_for_model = total_available_vram - (len(system.gpus) * VRAM_OVERHEAD_PER_GPU_GB)

    logger.info(f"System RAM: {system.total_ram_gb:.2f} GB (Available for model: {available_ram_for_model:.2f} GB)")
    logger.info(f"System VRAM (Total): {total_available_vram:.2f} GB (Available for model: {available_vram_for_model:.2f} GB)")

    # Check RAM feasibility
    if estimated_ram_gb > available_ram_for_model:
        raise FeasibilityError(
            f"Insufficient system RAM detected ({system.total_ram_gb:.2f} GB total, "
            f"{available_ram_for_model:.2f} GB available for model after {RAM_OVERHEAD_GB:.1f} GB overhead). "
            f"Estimated RAM required for model: {estimated_ram_gb:.2f} GB. "
            "Please try a smaller or more quantized model, or free up system memory."
        )

    # Check VRAM feasibility (only if GPUs are detected and VRAM is estimated)
    if system.gpus and estimated_vram_gb > 0: # Only check if VRAM estimation is meaningful
        if estimated_vram_gb > available_vram_for_model:
            raise FeasibilityError(
                f"Insufficient GPU VRAM detected ({total_available_vram:.2f} GB total, "
                f"{available_vram_for_model:.2f} GB available for model after {VRAM_OVERHEAD_PER_GPU_GB:.1f} GB/GPU overhead). "
                f"Estimated VRAM required for model: {estimated_vram_gb:.2f} GB. "
                "Please try a smaller or more quantized model, or reduce GPU offload."
            )
    elif estimated_vram_gb > 0 and not system.gpus:
        logger.warning("Model has VRAM requirements but no GPUs were detected. Assuming CPU-only inference.")
        # If VRAM is estimated but no GPUs, it implies the model might be too large for CPU-only.
        # This case is implicitly handled by the RAM check, as VRAM requirements often translate
        # to increased RAM usage if offloaded to CPU.

    logger.info("Feasibility check passed. Model is likely to fit within system resources.")

def should_recommend_mlock(model: ModelProfile, system: SystemProfile, safety_margin_ratio: float = 0.8) -> bool:
    """
    Determines if the --mlock argument should be recommended based on model size and available RAM.
    The recommendation is conservative, using a safety margin.

    Args:
        model (ModelProfile): The profile of the GGUF model.
        system (SystemProfile): The detected system hardware profile.
        safety_margin_ratio (float): The ratio of total RAM that the model size should not exceed
                                     to recommend mlock (e.g., 0.8 for 80%).

    Returns:
        bool: True if --mlock should be recommended, False otherwise.
    """
    logger.info("Evaluating --mlock recommendation...")

    try:
        model_file_size_bytes = os.path.getsize(model.file_path)
        model_file_size_gb = round(model_file_size_bytes / (1024**3), 2)
    except FileNotFoundError:
        logger.warning(f"Model file not found for mlock recommendation: {model.file_path}. Cannot recommend --mlock.")
        return False
    except Exception as e:
        logger.warning(f"Could not get model file size for mlock recommendation {model.file_path}: {e}. Cannot recommend --mlock.")
        return False

    # Account for system overhead, similar to feasibility check, but potentially more conservative
    # as mlock locks the entire process memory, not just the model.
    # A fixed overhead is a simplification; actual overhead can vary.
    RAM_OVERHEAD_FOR_MLOCK_GB = 2.0 # A smaller, more general overhead for mlock consideration

    available_ram_for_mlock = system.total_ram_gb - RAM_OVERHEAD_FOR_MLOCK_GB
    
    # Recommend mlock only if the model size is comfortably within the available RAM
    # after accounting for a safety margin and system overhead.
    # The model's actual memory usage might be slightly higher than its file size due to
    # KV cache, context, etc., but for mlock, the primary concern is the total resident set size.
    # We use the file size as the primary metric for this conservative check.
    
    # Check if system RAM detection was successful
    if system.total_ram_gb <= 0:
        logger.warning("Total RAM not detected or is zero. Cannot recommend --mlock.")
        return False

    # Check if the model file size is less than the available RAM multiplied by the safety margin
    if model_file_size_gb < (available_ram_for_mlock * safety_margin_ratio):
        logger.info(f"Model size ({model_file_size_gb:.2f} GB) is less than "
                    f"{safety_margin_ratio*100:.0f}% of available RAM ({available_ram_for_mlock:.2f} GB). "
                    f"Recommending --mlock.")
        return True
    else:
        logger.info(f"Model size ({model_file_size_gb:.2f} GB) is too large to comfortably fit "
                    f"within {safety_margin_ratio*100:.0f}% of available RAM ({available_ram_for_mlock:.2f} GB). "
                    f"Omitting --mlock recommendation.")
        return False