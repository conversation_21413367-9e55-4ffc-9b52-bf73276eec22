import logging
import subprocess
import re
import asyncio
import httpx
import time
from typing import List, Optional, Dict, Any, IO, Callable
from llama_tune.core.data_models import SystemProfile, ModelProfile, OptimalConfiguration, BenchmarkResult

# Define a type alias for the progress callback function
ProgressCallback = Callable[[str, str, int, int, Optional[float]], None]

from llama_tune.analyzer.analyzer import get_system_profile, get_model_profile

logger = logging.getLogger(__name__)

class BenchmarkingEngine:
    """
    Orchestrates the automated benchmarking process.
    """

    def run_benchmark(self, model_path: str, ctx_size: int, use_case: str, progress_callback: Optional[ProgressCallback] = None) -> OptimalConfiguration:
        """
        Executes the full hardware and model analysis as a prerequisite
        before starting the benchmark.
        """
        logger.info("BenchmarkingEngine: Initiating benchmark process.")

        system_profile = get_system_profile()
        logger.info(f"BenchmarkingEngine: Detected System Profile: {system_profile}")

        model_profile = get_model_profile(model_path)
        logger.info(f"BenchmarkingEngine: Detected Model Profile: {model_profile}")

        logger.info(f"Benchmarking for model: {model_profile.file_path} with context size: {ctx_size}")
        
        # Total phases for the overall progress bar
        total_phases = 1
        if use_case == "multi-user-server":
            total_phases = 2

        # Phase 1: GPU Offload Benchmark
        if progress_callback:
            progress_callback("Phase 1/{} GPU Offload".format(total_phases), "Starting...", 0, 1)
        best_benchmark_result = self._run_gpu_offload_benchmark(model_profile, ctx_size, progress_callback)

        optimal_config = OptimalConfiguration(
            system_profile=system_profile,
            model_profile=model_profile,
            best_benchmark_result=best_benchmark_result,
            generated_command="", # This will be generated by OutputGenerator
            notes=["GPU offload benchmark completed."],
            ctx_size=ctx_size,
            sampling_parameters={},
            use_case=use_case
        )

        # Phase 2: Throughput Benchmark (Batching and Concurrency)
        if use_case == "multi-user-server":
            logger.info("BenchmarkingEngine: User goal is multi-user server deployment. Starting throughput benchmark (batching and concurrency).")
            optimal_config.notes.append("Throughput benchmark (batching and concurrency) initiated.")
            if progress_callback:
                progress_callback("Phase 2/{} Throughput".format(total_phases), "Starting...", 0, 1)
            best_throughput_result = self._run_throughput_benchmark(
                model_profile,
                ctx_size,
                optimal_config.best_benchmark_result.n_gpu_layers, # Use optimal n_gpu_layers from previous phase
                progress_callback
            )
            # Update the best_benchmark_result with the one from throughput if it's better for generation_speed_tps
            # For multi-user, throughput (generation_speed_tps) is the primary metric
            if best_throughput_result.generation_speed_tps > optimal_config.best_benchmark_result.generation_speed_tps:
                optimal_config.best_benchmark_result = best_throughput_result
                optimal_config.notes.append(f"Optimal throughput configuration found: batch_size={best_throughput_result.batch_size}, parallel_level={best_throughput_result.parallel_level}")
            else:
                optimal_config.notes.append("Throughput benchmark completed, but GPU offload result was better or equal.")
        
        return optimal_config

    def _run_gpu_offload_benchmark(self, model_profile: ModelProfile, ctx_size: int, progress_callback: Optional[ProgressCallback] = None) -> BenchmarkResult:
        """
        Task 1 (AC: 1): In the BenchmarkingEngine component, create a method for the GPU offload benchmark phase.
        Task 2 (AC: 1, 2): This method should loop, incrementing the --n-gpu-layers value.
        Inside the loop, it must call the external llama-bench process with the current parameter set.
        Task 3 (AC: 3): Implement logic to parse the stdout from llama-bench to extract both prompt processing speed (pp) and token generation speed (tg).
        Task 4 (AC: 4): Wrap the subprocess call in error handling that can catch a crash and interpret it as the VRAM limit, which should gracefully end the loop.
        Task 5 (AC: 5): Store the performance results for each successful run. After the loop, analyze the results to find the configuration with the best performance.
        """
        logger.info("Starting GPU offload benchmark...")
        
        benchmark_results: List[BenchmarkResult] = []
        best_performance: float = -1.0
        best_result: Optional[BenchmarkResult] = None

        # Start from 0 layers and go up to the model's total layer count
        total_steps = model_profile.layer_count + 1
        for i, n_gpu_layers in enumerate(range(0, total_steps)):
            step_description = f"Testing --n-gpu-layers {n_gpu_layers}/{model_profile.layer_count}"
            logger.info(f"Benchmarking with --n-gpu-layers {n_gpu_layers}")
            
            command = [
                "llama-bench",
                "-m", model_profile.file_path,
                "-p", f"\"--n-gpu-layers {n_gpu_layers} --ctx-size {ctx_size}\"",
                "-n", "128", # Number of tokens to generate
                "-c", str(ctx_size) # Context size
            ]
            
            try:
                process = subprocess.run(
                    command,
                    capture_output=True,
                    text=True,
                    check=True,
                    encoding='utf-8'
                )
                stdout = process.stdout
                stderr = process.stderr

                if progress_callback:
                    progress_callback("Phase 1/2 GPU Offload", step_description, i + 1, total_steps)

                if stderr:
                    logger.warning(f"llama-bench stderr for n_gpu_layers={n_gpu_layers}:\n{stderr}")

                # Parse stdout for prompt processing speed and token generation speed
                pp_speed_match = re.search(r"prompt processing speed:\s*([\d.]+)\s*t/s", stdout)
                tg_speed_match = re.search(r"token generation speed:\s*([\d.]+)\s*t/s", stdout)

                pp_speed = float(pp_speed_match.group(1)) if pp_speed_match else 0.0
                tg_speed = float(tg_speed_match.group(1)) if tg_speed_match else 0.0

                current_result = BenchmarkResult(
                    n_gpu_layers=n_gpu_layers,
                    prompt_speed_tps=pp_speed,
                    generation_speed_tps=tg_speed,
                    batch_size=None, # Not applicable for this benchmark phase
                    parallel_level=None # Not applicable for this benchmark phase
                )
                benchmark_results.append(current_result)
                logger.info(f"Result for n_gpu_layers={n_gpu_layers}: PP Speed={pp_speed:.2f} t/s, TG Speed={tg_speed:.2f} t/s")
                if progress_callback:
                    progress_callback("Phase 1/2 GPU Offload", step_description, i + 1, total_steps, tg_speed)

                # For simplicity, let's consider generation speed as the primary metric for "best performance"
                # A more sophisticated approach might combine both or prioritize based on use case.
                if tg_speed > best_performance:
                    best_performance = tg_speed
                    best_result = current_result

            except subprocess.CalledProcessError as e:
                logger.error(f"llama-bench crashed for --n-gpu-layers {n_gpu_layers}. This likely indicates a VRAM limit.")
                logger.error(f"Command: {' '.join(e.cmd)}")
                logger.error(f"Return Code: {e.returncode}")
                logger.error(f"Stdout:\n{e.stdout}")
                logger.error(f"Stderr:\n{e.stderr}")
                # Gracefully end the loop as VRAM limit is reached
                break
            except FileNotFoundError:
                logger.error("llama-bench executable not found. Please ensure it's in your PATH.")
                break
            except Exception as e:
                logger.error(f"An unexpected error occurred during llama-bench execution: {e}")
                break
        
        if not best_result and benchmark_results:
            # If no crash occurred but no best_result was found (e.g., all speeds were 0),
            # pick the first successful result as a fallback.
            best_result = benchmark_results[0]
        elif not best_result:
            # If no successful runs at all, return a default/empty BenchmarkResult
            logger.warning("No successful benchmark runs for GPU offload.")
            return BenchmarkResult(
                n_gpu_layers=0,
                prompt_speed_tps=0.0,
                generation_speed_tps=0.0,
                batch_size=None,
                parallel_level=None
            )

        logger.info(f"Optimal GPU offload configuration: {best_result.n_gpu_layers} layers with TG Speed={best_result.generation_speed_tps:.2f} t/s")
        return best_result

    async def _send_completion_request(self, client: httpx.AsyncClient, prompt: str, max_tokens: int, temperature: float = 0.7) -> Dict[str, Any]:
        """Sends a completion request to the llama-server."""
        try:
            response = await client.post(
                "http://localhost:8080/completion",
                json={
                    "prompt": prompt,
                    "n_predict": max_tokens,
                    "temperature": temperature,
                    "stream": False
                },
                timeout=60.0 # Increased timeout for potentially long generations
            )
            response.raise_for_status()
            return response.json()
        except httpx.RequestError as e:
            logger.error(f"HTTP request failed: {e}")
            return {"error": str(e)}
        except Exception as e:
            logger.error(f"An unexpected error occurred during HTTP request: {e}")
            return {"error": str(e)}

    async def _simulate_concurrent_clients(self, num_clients: int, prompt: str, max_tokens: int) -> float:
        """
        Simulates multiple concurrent clients sending requests to llama-server.
        Returns total tokens/second.
        """
        logger.info(f"Simulating {num_clients} concurrent clients...")
        start_time = time.time()
        
        async with httpx.AsyncClient() as client:
            tasks = [
                self._send_completion_request(client, prompt, max_tokens)
                for _ in range(num_clients)
            ]
            responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        total_generated_tokens = 0
        successful_requests = 0
        for res in responses:
            if isinstance(res, dict) and "content" in res:
                total_generated_tokens += len(res["content"].split()) # Simple token count by splitting words
                successful_requests += 1
            else:
                logger.error(f"Client request failed or returned unexpected format: {res}")

        end_time = time.time()
        duration = end_time - start_time

        if successful_requests == 0 or duration == 0:
            logger.warning("No successful requests or zero duration in client simulation.")
            return 0.0
        
        total_throughput = total_generated_tokens / duration
        logger.info(f"Client simulation completed. Total generated tokens: {total_generated_tokens}, Duration: {duration:.2f}s, Throughput: {total_throughput:.2f} t/s")
        return total_throughput

    def _analyze_throughput_results(self, results: List[BenchmarkResult], n_gpu_layers: int) -> BenchmarkResult:
        """
        Analyzes a list of BenchmarkResult objects to find the one with the highest generation_speed_tps.
        """
        if not results:
            logger.warning("No benchmark results provided for analysis.")
            return BenchmarkResult(
                n_gpu_layers=n_gpu_layers,
                prompt_speed_tps=0.0,
                generation_speed_tps=0.0,
                batch_size=None,
                parallel_level=None
            )

        best_result = results[0]
        for result in results:
            if result.generation_speed_tps > best_result.generation_speed_tps:
                best_result = result
        return best_result

    def _run_throughput_benchmark(self, model_profile: ModelProfile, ctx_size: int, n_gpu_layers: int, progress_callback: Optional[ProgressCallback] = None) -> BenchmarkResult:
        """
        Task 1 (AC: 1): In the BenchmarkingEngine component, create a method for the continuous batching benchmark phase.
        Task 3 (AC: 2, 3): The method must run the `llama-server` executable as a subprocess and then simulate multiple, concurrent clients sending requests to it.
        Task 4 (AC: 2): The simulation should loop through different batching parameter values.
        Task 5 (AC: 4): For each run, capture and calculate the total server throughput. The process should identify the settings that yield the best results.
        """
        logger.info("Starting throughput benchmark (batching and concurrency)...")

        benchmark_results: List[BenchmarkResult] = []

        # Define batching parameters to test
        # These values can be tuned based on typical use cases or further research
        batch_sizes = [4, 8, 16, 32] # Example batch sizes
        parallel_levels = [2, 4, 8] # Example concurrent client levels

        # A simple prompt for testing
        test_prompt = "Tell me a short story about a brave knight and a dragon."
        max_tokens_to_generate = 128

        total_steps = len(batch_sizes) * len(parallel_levels)
        current_step_count = 0

        for batch_size in batch_sizes:
            for parallel_level in parallel_levels:
                current_step_count += 1
                step_description = f"Testing batch_size={batch_size}, parallel_level={parallel_level}"
                logger.info(f"Benchmarking with --batch-size {batch_size} and --parallel-level {parallel_level}")
                
                if progress_callback:
                    progress_callback("Phase 2/2 Throughput", step_description, current_step_count, total_steps)

                # Command to start llama-server with continuous batching
                server_command = [
                    "llama-server",
                    "-m", model_profile.file_path,
                    "--n-gpu-layers", str(n_gpu_layers),
                    "--ctx-size", str(ctx_size),
                    "--batch-size", str(batch_size),
                    "--threads", "4", # Example: Use a fixed number of threads for the server
                    "--port", "8080" # Ensure a consistent port
                ]

                server_process: Optional[subprocess.Popen] = None
                try:
                    logger.info(f"Starting llama-server: {' '.join(server_command)}")
                    server_process = subprocess.Popen(
                        server_command,
                        stdout=subprocess.PIPE,
                        stderr=subprocess.PIPE,
                        text=True,
                        encoding='utf-8'
                    )

                    # Give the server some time to start up
                    time.sleep(5)

                    # Check if server started successfully by looking for a specific log message
                    server_startup_successful = False
                    if server_process.stdout:
                        for _ in range(10): # Check up to 10 lines
                            line = server_process.stdout.readline()
                            if "HTTP server listening" in line:
                                server_startup_successful = True
                                logger.info("llama-server started successfully.")
                                break
                            if not line: # No more output
                                break
                            logger.debug(f"Server startup output: {line.strip()}")

                    if not server_startup_successful:
                        logger.error("llama-server did not start successfully or timed out.")
                        if server_process.stderr:
                            server_stderr = server_process.stderr.read()
                            logger.error(f"llama-server stderr:\n{server_stderr}")
                        if server_process.poll() is not None: # If process exited
                            logger.error(f"llama-server exited with code: {server_process.returncode}")
                        continue # Skip to next iteration

                    # Run concurrent client simulation
                    total_throughput = asyncio.run(
                        self._simulate_concurrent_clients(parallel_level, test_prompt, max_tokens_to_generate)
                    )

                    current_result = BenchmarkResult(
                        n_gpu_layers=n_gpu_layers,
                        prompt_speed_tps=0.0, # Not directly measured in this phase
                        generation_speed_tps=total_throughput,
                        batch_size=batch_size,
                        parallel_level=parallel_level
                    )
                    benchmark_results.append(current_result)
                    logger.info(f"Result for batch_size={batch_size}, parallel_level={parallel_level}: Throughput={total_throughput:.2f} t/s")
                    if progress_callback:
                        progress_callback("Phase 2/2 Throughput", step_description, current_step_count, total_steps, total_throughput)

                except FileNotFoundError:
                    logger.error("llama-server executable not found. Please ensure it's in your PATH.")
                    break # Exit both loops
                except Exception as e:
                    logger.error(f"An unexpected error occurred during llama-server or client execution: {e}")
                    if server_process and server_process.stderr:
                        server_stderr = server_process.stderr.read()
                        logger.error(f"llama-server stderr during error: {server_stderr}")
                    break # Exit both loops
                finally:
                    if server_process and server_process.poll() is None:
                        logger.info("Terminating llama-server process.")
                        server_process.terminate()
                        server_process.wait(timeout=10) # Wait for process to terminate
                        if server_process.poll() is None:
                            logger.warning("llama-server did not terminate gracefully. Killing it.")
                            server_process.kill()

            if server_process and server_process.poll() is not None:
                # If server process died, it means something is wrong, break outer loop too
                break
        
        return self._analyze_throughput_results(benchmark_results, n_gpu_layers)