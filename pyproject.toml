[tool.poetry]
name = "llama-tune"
version = "0.1.0"
description = "A tool to optimize LLM inference with llama.cpp"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = ">=3.11,<4.0"
typer = {extras = ["all"], version = "^0.9.0"}
pytest = "^8.4.1"
psutil = "^5.9.0"
questionary = "^2.1.0"
httpx = "^0.27.0"
rich = "^13.7.1"

[tool.poetry.group.dev.dependencies]
black = "^23.0.0"
isort = "^5.0.0"
flake8 = "^6.0.0"
pytest-mock = "^3.12.0" # Added for mocker fixture
pytest = "^8.4.1"
pytest-asyncio = "^1.0.0"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"