# Story 3.3: Benchmark Continuous Batching for Server Throughput

## Status: Approved

## Story

- As a Researcher (<PERSON>)
- I want the benchmark tool to test and tune continuous batching parameters
- so that I can find the optimal configuration for maximum server throughput under concurrent load.

## Acceptance Criteria (ACs)

1. After determining the optimal `--n-gpu-layers`, this benchmark phase begins.
2. The tool iteratively tests different values for continuous batching parameters, such as `--batch-size`.
3. The test for this phase must simulate concurrent requests to accurately measure overall server throughput.
4. The process identifies the batching configuration that results in the highest aggregate tokens/second across all simulated requests.
5. This benchmark phase is automatically run only when the user's goal is a multi-user server deployment.

## Tasks / Subtasks

- [ ] Task 1 (AC: 1): In the `BenchmarkingEngine` component, create a method for the continuous batching benchmark phase.
- [ ] Task 2 (AC: 5): This method should only be executed if the user's selected use case requires server throughput tuning.
- [ ] Task 3 (AC: 2, 3): The method must run the `llama-server` executable as a subprocess and then simulate multiple, concurrent clients sending requests to it (e.g., using Python's `asyncio` or `threading`).
- [ ] Task 4 (AC: 2): The simulation should loop through different batching parameter values.
- [ ] Task 5 (AC: 4): For each run, capture and calculate the total server throughput. The process should identify the settings that yield the best results.
- [ ] Task 6 (AC: 4): Write unit tests for the logic that analyzes throughput results to determine the optimal configuration.

## Dev Notes

This is a complex test. It requires starting a `llama-server` subprocess and then running a separate client process/thread to send requests to it. As per our architecture, using `llama-server` is essential here because continuous batching is a server-specific feature.

### Testing

Dev Note: Story Requires the following tests:
- [x] `pytest` Unit Tests: (location: `tests/unit/benchmarker/`), coverage requirement: 85%. Test the logic for analyzing server throughput results.
- [x] Integration Test: Test the `BenchmarkingEngine`'s ability to start and stop a mock `llama-server` process for testing purposes.
- [ ] E2E Test

**Manual Test Steps**: This is difficult to test manually without a controlled environment. The primary validation method will be the automated tests.

## Dev Agent Record

### Agent Model Used: {{Agent Model Name/Version}}

### Debug Log References
[[LLM: (Dev Agent) If the debug is logged to during the current story progress, create a table with the debug log and the specific task section in the debug log - do not repeat all the details in the story]]

### Completion Notes List
[[LLM: (Dev Agent) Anything the SM/PO needs to know that deviated from the story that might impact drafting the next story.]]

### Change Log
[[LLM: (Dev Agent) Track document versions and changes during development that deviate from story dev start]]

| Date | Version | Description | Author |
| :--- | :------ | :---------- | :----- |