# Prompts

## Full Stack Developer

### Implement Story
Pre-requisite:
In the IDE, insert specific story file path to the prompt below such as @/docs/stories/3.1-initiate-benchmark.md

Prompt:
```
Please implement the story @docs/stories/3.3c-e2e-throughput-benchmark.md. Create or modify the necessary files in my workspace and write the required tests as specified. Ensure the code meets all acceptance criteria defined in the user story and aligns with the architecture document @docs/architecture.md. Execute tests with command `poetry run pytest -v --ignore=vendor`.
```

## Product Owner

### Review Code of Epic
Pre-requisite: 
- In the Gemini Gems's Fullstack team, switch to Product Owner agent (<PERSON>) with `*agent po`.
- Import code from GitHub repository such as https://github.com/limcheekin/llama-tune/tree/bmad

Prompt:
```
Hi <PERSON>, please carefully review the imported GitHub repository in detail, verify code implemented by the developer agent adhere to the architecture document (docs/architecture.md) and detail user story documents (docs/stories/*.md) of the Epic X we defined, identify any deviation and misalignment.
```
**Note:** Need to change `X` to specific epic.

## QA/Code Reviewer

### Review Code of Story
Prompt:
```
Please carefully review the code implemented in detail, ensure it strictly adhere to the detail user story document (@docs/stories/3.3c-e2e-throughput-benchmark.md) and the architecture document (@docs/architecture.md).
```