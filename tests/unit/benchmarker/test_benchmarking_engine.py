import subprocess
import pytest
from unittest.mock import MagicMock, patch, ANY
from llama_tune.analyzer.analyzer import get_system_profile, get_model_profile
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.core.data_models import ModelProfile, SystemProfile, BenchmarkResult, OptimalConfiguration

@pytest.fixture
def mock_model_profile():
    return ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=30,
        quantization_type="Q4_0"
    )

@pytest.fixture
def mock_system_profile():
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=16.0,
        gpus=[],
        numa_detected=False,
        blas_backend="BLIS"
    )

@pytest.fixture
def benchmarking_engine():
    return BenchmarkingEngine()

def test_run_benchmark_calls_analyzer_and_gpu_offload(benchmarking_engine, mock_model_profile, mock_system_profile):
    with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile) as mock_get_system_profile, \
         patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile) as mock_get_model_profile, \
         patch.object(benchmarking_engine, '_run_gpu_offload_benchmark') as mock_gpu_offload, \
         patch.object(benchmarking_engine, '_run_throughput_benchmark') as mock_throughput_benchmark:
        
        mock_gpu_offload.return_value = BenchmarkResult(
            n_gpu_layers=10,
            prompt_speed_tps=50.0,
            generation_speed_tps=20.0,
            batch_size=None,
            parallel_level=None
        )
        mock_throughput_benchmark.return_value = BenchmarkResult(
            n_gpu_layers=10,
            prompt_speed_tps=0.0,
            generation_speed_tps=30.0, # Better throughput
            batch_size=8,
            parallel_level=4
        )

        result = benchmarking_engine.run_benchmark("/path/to/model.gguf", 512, "default", MagicMock())

        mock_get_system_profile.assert_called_once()
        mock_get_model_profile.assert_called_once_with("/path/to/model.gguf")
        mock_gpu_offload.assert_called_once_with(mock_model_profile, 512, ANY)
        mock_throughput_benchmark.assert_not_called() # Should not be called for "default" use case
        assert isinstance(result, OptimalConfiguration)
        assert result.best_benchmark_result.n_gpu_layers == 10

    def test_run_benchmark_calls_throughput_benchmark_for_multi_user(self, benchmarking_engine, mock_model_profile, mock_system_profile):
        with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile), \
             patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile), \
             patch.object(benchmarking_engine, '_run_gpu_offload_benchmark') as mock_gpu_offload, \
             patch.object(benchmarking_engine, '_run_throughput_benchmark') as mock_throughput_benchmark:

            mock_gpu_offload.return_value = BenchmarkResult(
                n_gpu_layers=10,
                prompt_speed_tps=50.0,
                generation_speed_tps=20.0,
                batch_size=None,
                parallel_level=None
            )
            mock_throughput_benchmark.return_value = BenchmarkResult(
                n_gpu_layers=10,
                prompt_speed_tps=0.0,
                generation_speed_tps=30.0, # Better throughput
                batch_size=8,
                parallel_level=4
            )

            result = benchmarking_engine.run_benchmark("/path/to/model.gguf", 512, "multi-user-server", MagicMock())

            mock_gpu_offload.assert_called_once_with(mock_model_profile, 512, ANY)
            mock_throughput_benchmark.assert_called_once_with(mock_model_profile, 512, 10, ANY)
            assert isinstance(result, OptimalConfiguration)
            assert result.best_benchmark_result.generation_speed_tps == 30.0 # Should pick the better throughput result
            assert result.best_benchmark_result.batch_size == 8
            assert result.best_benchmark_result.parallel_level == 4

    def test_run_benchmark_progress_callback_called(self, benchmarking_engine, mock_model_profile, mock_system_profile):
        with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile), \
             patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile), \
             patch.object(benchmarking_engine, '_run_gpu_offload_benchmark') as mock_gpu_offload, \
             patch.object(benchmarking_engine, '_run_throughput_benchmark') as mock_throughput_benchmark:

            mock_gpu_offload.return_value = BenchmarkResult(
                n_gpu_layers=10,
                prompt_speed_tps=50.0,
                generation_speed_tps=20.0,
                batch_size=None,
                parallel_level=None
            )
            mock_throughput_benchmark.return_value = BenchmarkResult(
                n_gpu_layers=10,
                prompt_speed_tps=0.0,
                generation_speed_tps=30.0,
                batch_size=8,
                parallel_level=4
            )

            mock_progress_callback = MagicMock()
            benchmarking_engine.run_benchmark("/path/to/model.gguf", 512, "multi-user-server", mock_progress_callback)

            # Check that the progress callback was called for both phases
            assert mock_progress_callback.call_count >= 2 # At least once for each phase start
            mock_progress_callback.assert_any_call("Phase 1/2 GPU Offload", "Starting...", 0, 1)
            mock_progress_callback.assert_any_call("Phase 2/2 Throughput", "Starting...", 0, 1)

class TestGpuOffloadBenchmark:
   @pytest.mark.parametrize("stdout_fixture, expected_pp, expected_tg", [
       ("llama_bench_output_success_1.txt", 100.5, 20.1),
       ("llama_bench_output_success_2.txt", 90.0, 18.5),
       ("llama_bench_output_no_speed.txt", 0.0, 0.0),
   ])
   def test_parse_llama_bench_output_success(self, benchmarking_engine, mock_model_profile, stdout_fixture, expected_pp, expected_tg, request):
       # Load stdout content from fixture file
       fixture_path = f"tests/unit/benchmarker/fixtures/{stdout_fixture}"
       try:
           with open(fixture_path, 'r') as f:
               mock_stdout = f.read()
       except FileNotFoundError:
           pytest.fail(f"Fixture file not found: {fixture_path}")

       # Create a list of mock process results for each iteration
       mock_results = []
       for i in range(mock_model_profile.layer_count + 1):
           mock_process = MagicMock()
           mock_process.stdout = mock_stdout
           mock_process.stderr = ""
           mock_results.append(mock_process)

       mock_progress_callback = MagicMock()
       with patch('subprocess.run', side_effect=mock_results) as mock_sub_run:
           result = benchmarking_engine._run_gpu_offload_benchmark(mock_model_profile, 512, mock_progress_callback)
           
           assert result.prompt_speed_tps == expected_pp
           assert result.generation_speed_tps == expected_tg
           assert mock_sub_run.call_count == mock_model_profile.layer_count + 1
           # Verify progress callback was called for each step (twice per iteration: start and result)
           assert mock_progress_callback.call_count == (mock_model_profile.layer_count + 1) * 2
           mock_progress_callback.assert_called_with(
               "Phase 1/2 GPU Offload",
               f"Testing --n-gpu-layers {mock_model_profile.layer_count}/{mock_model_profile.layer_count}",
               mock_model_profile.layer_count + 1,
               mock_model_profile.layer_count + 1,
               expected_tg
           )

   def test_gpu_offload_benchmark_vram_limit_handling(self, benchmarking_engine, mock_model_profile):
       mock_results = []
       for i in range(10): # Simulate 10 successful runs (layers 0-9)
           mock_process = MagicMock()
           mock_process.stdout = "prompt processing speed: 100.0 t/s\ntoken generation speed: 20.0 t/s"
           mock_process.stderr = ""
           mock_results.append(mock_process)
       # Add one failing run to simulate OOM for layer 10
       mock_results.append(subprocess.CalledProcessError(returncode=1, cmd="llama-bench", output="", stderr="CUDA out of memory"))

       mock_progress_callback = MagicMock()
       with patch('subprocess.run', side_effect=mock_results) as mock_sub_run:
           result = benchmarking_engine._run_gpu_offload_benchmark(mock_model_profile, 512, mock_progress_callback)
           
           # Expect the loop to stop after the first successful run, so the best result is from n_gpu_layers=0
           assert result.n_gpu_layers == 0
           assert result.prompt_speed_tps == 100.0
           assert result.generation_speed_tps == 20.0
           assert mock_sub_run.call_count == 11 # Should run 11 times (0-10) and then crash on the 11th (index 10)
           # Verify progress callback was called for each successful step before the crash (twice per iteration)
           assert mock_progress_callback.call_count == 20 # Called twice for layers 0-9 (10 layers × 2 calls each)
           mock_progress_callback.assert_called_with(
               "Phase 1/2 GPU Offload",
               "Testing --n-gpu-layers 9/30",
               10, # 10th step (index 9)
               31, # Total steps (0-30)
               20.0
           )

   def test_gpu_offload_benchmark_no_llama_bench_executable(self, benchmarking_engine, mock_model_profile):
       mock_progress_callback = MagicMock()
       with patch('subprocess.run', side_effect=FileNotFoundError) as mock_sub_run:
           result = benchmarking_engine._run_gpu_offload_benchmark(mock_model_profile, 512, mock_progress_callback)
           
           assert result.n_gpu_layers == 0
           assert result.prompt_speed_tps == 0.0
           assert result.generation_speed_tps == 0.0
           mock_sub_run.assert_called_once() # Should attempt to run once and fail
           mock_progress_callback.assert_not_called() # No progress updates if executable not found

   def test_gpu_offload_benchmark_empty_results(self, benchmarking_engine, mock_model_profile):
       mock_results = []
       for i in range(mock_model_profile.layer_count + 1):
           mock_process = MagicMock()
           mock_process.stdout = "Some other output without speed metrics"
           mock_process.stderr = ""
           mock_results.append(mock_process)

       mock_progress_callback = MagicMock()
       with patch('subprocess.run', side_effect=mock_results) as mock_sub_run:
           result = benchmarking_engine._run_gpu_offload_benchmark(mock_model_profile, 512, mock_progress_callback)
           
           # If no speeds are found, it should return a default BenchmarkResult
           assert result.n_gpu_layers == 0
           assert result.prompt_speed_tps == 0.0
           assert result.generation_speed_tps == 0.0
           # The loop should run for all layers if no crash occurs and no speeds are found
           assert mock_sub_run.call_count == mock_model_profile.layer_count + 1
           # Progress callback should still be called for each step, even if speed is 0.0 (twice per iteration)
           assert mock_progress_callback.call_count == (mock_model_profile.layer_count + 1) * 2
           mock_progress_callback.assert_called_with(
               "Phase 1/2 GPU Offload",
               f"Testing --n-gpu-layers {mock_model_profile.layer_count}/{mock_model_profile.layer_count}",
               mock_model_profile.layer_count + 1,
               mock_model_profile.layer_count + 1,
               0.0
           )

class TestThroughputBenchmark:
   @pytest.mark.asyncio
   @pytest.mark.parametrize("mock_throughput, expected_batch_size, expected_parallel_level", [
       (15.0, 4, 2),
       (25.0, 8, 4),
       (35.0, 16, 8),
   ])
   async def test_run_throughput_benchmark_success(self, benchmarking_engine, mock_model_profile, mock_throughput, expected_batch_size, expected_parallel_level):
       # Mock subprocess.Popen for llama-server
       mock_server_process = MagicMock()
       mock_server_process.stdout.readline.return_value = "HTTP server listening" # Simulate server startup message
       mock_server_process.poll.return_value = None # Server stays running
       mock_server_process.stderr.read.return_value = ""

       # Mock asyncio.run to control the simulated client throughput
       # Return higher throughput for the expected combination
       call_count = 0
       def mock_asyncio_run(coro):
           nonlocal call_count
           call_count += 1
           # The batch_size and parallel_level are determined by the order of calls
           # batch_sizes = [4, 8, 16, 32], parallel_levels = [2, 4, 8]
           # So call 1 = (4,2), call 2 = (4,4), call 3 = (4,8), call 4 = (8,2), etc.
           batch_sizes = [4, 8, 16, 32]
           parallel_levels = [2, 4, 8]
           batch_idx = (call_count - 1) // 3
           parallel_idx = (call_count - 1) % 3
           current_batch = batch_sizes[batch_idx]
           current_parallel = parallel_levels[parallel_idx]

           # Return the highest throughput for the expected combination
           if current_batch == expected_batch_size and current_parallel == expected_parallel_level:
               return mock_throughput
           else:
               return mock_throughput * 0.5  # Lower throughput for other combinations

       with patch('subprocess.Popen', return_value=mock_server_process) as mock_popen, \
            patch('asyncio.run', side_effect=mock_asyncio_run) as mock_asyncio_run_patch, \
            patch('time.sleep', return_value=None) as mock_sleep: # Mock sleep to speed up tests
           
           mock_progress_callback = MagicMock()
           result = benchmarking_engine._run_throughput_benchmark(mock_model_profile, 512, 10, mock_progress_callback)
           
           assert result.generation_speed_tps == mock_throughput
           assert result.batch_size == expected_batch_size
           assert result.parallel_level == expected_parallel_level
           
           # Verify server process was started and terminated
           mock_popen.assert_called()
           mock_server_process.terminate.assert_called()
           mock_server_process.wait.assert_called()

           # Verify progress callback was called for each step (twice per iteration: start and result)
           total_expected_calls = len([4, 8, 16, 32]) * len([2, 4, 8]) * 2
           assert mock_progress_callback.call_count == total_expected_calls

           # Verify that the expected combination was tested (should be in the call history)
           expected_call_found = False
           for call in mock_progress_callback.call_args_list:
               if (len(call[0]) >= 5 and
                   call[0][1] == f"Testing batch_size={expected_batch_size}, parallel_level={expected_parallel_level}" and
                   call[0][4] == mock_throughput):
                   expected_call_found = True
                   break
           assert expected_call_found, f"Expected call for batch_size={expected_batch_size}, parallel_level={expected_parallel_level} with throughput={mock_throughput} not found"

   @pytest.mark.asyncio
   async def test_run_throughput_benchmark_server_startup_failure(self, benchmarking_engine, mock_model_profile):
       # Simulate server failing to start
       mock_server_process = MagicMock()
       mock_server_process.stdout.readline.side_effect = ["", ""] # No startup message
       mock_server_process.poll.return_value = 1 # Server exits immediately
       mock_server_process.stderr.read.return_value = "Error: Port already in use"

       with patch('subprocess.Popen', return_value=mock_server_process) as mock_popen, \
            patch('asyncio.run', return_value=0.0) as mock_asyncio_run, \
            patch('time.sleep', return_value=None) as mock_sleep:
           
           mock_progress_callback = MagicMock()
           result = benchmarking_engine._run_throughput_benchmark(mock_model_profile, 512, 10, mock_progress_callback)
           
           assert result.generation_speed_tps == 0.0 # No successful runs
           mock_popen.assert_called()
           mock_asyncio_run.assert_not_called() # Client simulation should not run
           mock_server_process.terminate.assert_not_called() # Server didn't start, no need to terminate

           # Progress callback should be called for each step attempted (only once per step since server fails)
           # The algorithm tries a few combinations before giving up
           assert mock_progress_callback.call_count >= 3  # At least a few attempts

   @pytest.mark.asyncio
   async def test_run_throughput_benchmark_no_llama_server_executable(self, benchmarking_engine, mock_model_profile):
       with patch('subprocess.Popen', side_effect=FileNotFoundError) as mock_popen, \
            patch('asyncio.run', return_value=0.0) as mock_asyncio_run, \
            patch('time.sleep', return_value=None) as mock_sleep:
           
           mock_progress_callback = MagicMock()
           result = benchmarking_engine._run_throughput_benchmark(mock_model_profile, 512, 10, mock_progress_callback)
           
           assert result.generation_speed_tps == 0.0
           assert mock_popen.call_count >= 1  # Should attempt to run at least once and fail
           mock_asyncio_run.assert_not_called()
           # Progress callback is called for each attempt, even if executable not found
           assert mock_progress_callback.call_count >= 1

   @pytest.mark.asyncio
   async def test_run_throughput_benchmark_client_simulation_failure(self, benchmarking_engine, mock_model_profile):
       mock_server_process = MagicMock()
       mock_server_process.stdout.readline.side_effect = ["HTTP server listening", ""]
       mock_server_process.poll.return_value = None
       mock_server_process.stderr.read.return_value = ""

       with patch('subprocess.Popen', return_value=mock_server_process) as mock_popen, \
            patch('asyncio.run', side_effect=Exception("Client simulation error")) as mock_asyncio_run, \
            patch('time.sleep', return_value=None) as mock_sleep:
           
           mock_progress_callback = MagicMock()
           result = benchmarking_engine._run_throughput_benchmark(mock_model_profile, 512, 10, mock_progress_callback)
           
           assert result.generation_speed_tps == 0.0
           mock_popen.assert_called()
           assert mock_asyncio_run.call_count >= 1  # Should attempt client simulation at least once and fail
           mock_server_process.terminate.assert_called() # Server should still be terminated

           # Progress callback should be called for each step attempted
           assert mock_progress_callback.call_count >= 1  # At least one attempt

