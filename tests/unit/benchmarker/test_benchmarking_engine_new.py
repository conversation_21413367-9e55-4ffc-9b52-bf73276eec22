import pytest
from unittest.mock import MagicMock, patch
import subprocess
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.core.data_models import ModelProfile, BenchmarkResult

@pytest.fixture
def mock_model_profile():
    return ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=30,
        quantization_type="q4_0"
    )

@pytest.fixture
def benchmarking_engine():
    return BenchmarkingEngine()

@patch('llama_tune.benchmarker.benchmarking_engine.subprocess.run')
def test_check_n_gpu_layers_success(mock_subprocess_run, benchmarking_engine, mock_model_profile):
    mock_subprocess_run.return_value = MagicMock(
        stdout="prompt processing speed: 100.0 t/s\ntoken generation speed: 50.0 t/s",
        stderr="",
        returncode=0
    )
    result = benchmarking_engine._check_n_gpu_layers(mock_model_profile, 2048, 10)
    assert result is not None
    assert result.n_gpu_layers == 10
    assert result.prompt_speed_tps == 100.0
    assert result.generation_speed_tps == 50.0

@patch('llama_tune.benchmarker.benchmarking_engine.subprocess.run')
def test_check_n_gpu_layers_oom(mock_subprocess_run, benchmarking_engine, mock_model_profile):
    mock_subprocess_run.side_effect = subprocess.CalledProcessError(
        returncode=1, cmd="llama-bench", output="", stderr="OOM"
    )
    result = benchmarking_engine._check_n_gpu_layers(mock_model_profile, 2048, 20)
    assert result is None

@patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine._check_n_gpu_layers')
def test_run_gpu_offload_benchmark_binary_search(mock_check_n_gpu_layers, benchmarking_engine, mock_model_profile):
    def side_effect_func(model_profile, ctx_size, n_gpu_layers):
        if n_gpu_layers < 15:
            return BenchmarkResult(n_gpu_layers=n_gpu_layers, prompt_speed_tps=10.0, generation_speed_tps=float(n_gpu_layers), batch_size=None, parallel_level=None)
        else:
            return None

    mock_check_n_gpu_layers.side_effect = side_effect_func

    best_result = benchmarking_engine._run_gpu_offload_benchmark(mock_model_profile, 2048)

    assert best_result is not None
    assert best_result.n_gpu_layers == 14
    assert "OOM detected at --n-gpu-layers 15" in best_result.notes

@patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine._simulate_concurrent_clients')
@patch('llama_tune.benchmarker.benchmarking_engine.subprocess.Popen')
@patch('llama_tune.benchmarker.benchmarking_engine.time.sleep')
def test_run_throughput_benchmark_two_stage_search(mock_sleep, mock_popen, mock_simulate_clients, benchmarking_engine, mock_model_profile):
    # Mock server startup success
    mock_process = MagicMock()
    mock_process.stdout.readline.side_effect = ["HTTP server listening", "\n"] * 20 # Ensure enough lines for startup check
    mock_process.poll.return_value = None # Server is running
    mock_process.stderr.read.return_value = "" # Mock stderr to be empty
    mock_popen.return_value = mock_process

    # Mock client simulation to return increasing throughput
    mock_simulate_clients.side_effect = [
        10.0, 20.0, 30.0, 40.0, 50.0, 60.0, # Stage 1: Parallel levels
        100.0, 110.0, 120.0, 130.0, 140.0, 150.0, 160.0, 170.0, 180.0, 190.0 # Stage 2: Batch sizes
    ]

    best_result = benchmarking_engine._run_throughput_benchmark(mock_model_profile, 2048, 10)

    assert best_result is not None
    # Based on the mocked throughputs, the last batch size should be the best
    assert best_result.batch_size == 512
    assert best_result.parallel_level == 32 # This should be the best parallel level from stage 1
    assert best_result.generation_speed_tps == 190.0 # The highest throughput from stage 2

