import subprocess
import pytest

def test_interactive_wizard_starts_with_flag():
    """
    Verify that running llama-tune with --interactive flag prints the welcome message.
    """
    command = ["poetry", "run", "python", "-m", "llama_tune.cli", "--interactive"]
    result = subprocess.run(command, capture_output=True, text=True, check=False)
    
    assert result.returncode == 0
    assert "Welcome to the Llama-Tune Interactive Setup Wizard!" in result.stdout
    assert "This wizard will guide you through configuring optimal settings for your GGUF model." in result.stdout

def test_interactive_wizard_does_not_start_without_flag():
    """
    Verify that running llama-tune without --interactive flag does not print the welcome message.
    """
    # We need a dummy model path for the non-interactive run, as it's a required argument
    # For this E2E test, we're only concerned with the wizard's presence, not full functionality.
    # A non-existent path is fine as long as it doesn't trigger the wizard.
    command = ["poetry", "run", "python", "-m", "llama_tune.cli", "--model-path", "dummy_model.gguf"]
    result = subprocess.run(command, capture_output=True, text=True, check=False)
    
    # The command should exit with an error because dummy_model.gguf does not exist,
    # but it should NOT contain the wizard's welcome message.
    assert result.returncode != 0 # Expecting an error due to missing model file
    assert "Welcome to the Llama-Tune Interactive Setup Wizard!" not in result.stdout