import pytest
from unittest.mock import patch, MagicMock
from llama_tune.wizard.interactive_wizard import InteractiveWizard
from llama_tune.core.data_models import SystemProfile, ModelProfile, OptimalConfiguration, BenchmarkResult
from llama_tune.reporting.output_generator import generate_output
from llama_tune.analyzer.analyzer import FeasibilityError
import typer

@pytest.fixture
def mock_interactive_wizard():
    with patch('llama_tune.wizard.interactive_wizard.get_system_profile') as mock_get_system_profile, \
         patch('llama_tune.wizard.interactive_wizard.get_model_profile') as mock_get_model_profile, \
         patch('llama_tune.wizard.interactive_wizard.run_feasibility_check') as mock_run_feasibility_check, \
         patch('questionary.select') as mock_questionary_select, \
         patch('questionary.text') as mock_questionary_text:
        
        # Directly set the side_effect for questionary.text.ask() in each test
        # This mock will handle all calls to questionary.text().ask()
        mock_questionary_text.return_value.ask = MagicMock()
        # Reset the mock for each test run to ensure independent side_effects
        # This is not needed if side_effect is set directly on the mock in each test.
        # mock_questionary_text.return_value.ask.reset_mock()

        # Mock system profile
        mock_get_system_profile.return_value = SystemProfile(
            cpu_cores=8,
            total_ram_gb=32.0,
            gpus=[],
            numa_detected=False,
            blas_backend="cuBLAS"
        )

        # Mock model profile
        mock_get_model_profile.return_value = ModelProfile(
            file_path="/path/to/model.gguf",
            architecture="llama",
            layer_count=32,
            quantization_type="Q4_0"
        )

        # Mock feasibility check to always pass
        mock_run_feasibility_check.return_value = None

        # Mock questionary prompts
        mock_questionary_select.return_value.ask.return_value = "Conversational Chatbot"
        # side_effect will be set in individual test functions

        wizard = InteractiveWizard()
        yield wizard, mock_get_system_profile, mock_get_model_profile, mock_run_feasibility_check, mock_questionary_select, mock_questionary_text

def test_full_wizard_flow_generates_correct_command(mock_interactive_wizard):
    wizard, mock_get_system_profile, mock_get_model_profile, mock_run_feasibility_check, mock_questionary_select, mock_questionary_text = mock_interactive_wizard

    # Set side_effect for all questionary.text().ask() calls in this test
    mock_questionary_text.return_value.ask.side_effect = ["4096", "/path/to/model.gguf"]
    
    # Run the wizard
    with patch('typer.echo'), \
         patch('os.path.getsize', return_value=1 * 1024**3):  # Mock 1GB model file
        wizard.start()
    
    # Get the optimal configuration
    optimal_config = wizard.get_optimal_configuration()

    assert optimal_config is not None
    assert isinstance(optimal_config, OptimalConfiguration)

    # Verify system and model profiles are populated
    assert optimal_config.system_profile.cpu_cores == 8
    assert optimal_config.model_profile.file_path == "/path/to/model.gguf"
    assert optimal_config.best_benchmark_result.n_gpu_layers == 0 # Default for now

    # Verify user choices are reflected
    assert optimal_config.generated_command is not None
    assert "--ctx-size 4096" in optimal_config.generated_command
    assert "--temperature 0.7" in optimal_config.generated_command # From Conversational Chatbot preset
    assert "--top_p 0.9" in optimal_config.generated_command # From Conversational Chatbot preset
    assert "--repeat_penalty 1.1" in optimal_config.generated_command # From Conversational Chatbot preset
    assert "--presence_penalty 0.0" in optimal_config.generated_command # From Conversational Chatbot preset
    assert "--frequency_penalty 0.0" in optimal_config.generated_command # From Conversational Chatbot preset
    assert "--threads 8" in optimal_config.generated_command
    assert "--model /path/to/model.gguf" in optimal_config.generated_command

    # Verify OutputGenerator enhances the command correctly
    with patch('os.path.getsize', return_value=1 * 1024**3):  # Mock 1GB model file for generate_output
        final_command_output = generate_output(optimal_config, "default")
    assert "--use-cublas" in final_command_output # From system_profile.blas_backend
    assert "--numa distribute" not in final_command_output # Not detected in mock
    assert "--mlock" in final_command_output # Should be recommended as model is small and RAM is large

    # Verify notes are generated
    with patch('os.path.getsize', return_value=1 * 1024**3):  # Mock 1GB model file for verbose output
        verbose_output = generate_output(optimal_config, "verbose")
    assert "cuBLAS backend detected. '--use-cublas' added for GPU acceleration." in verbose_output
    assert "Model size is comfortably within available RAM. '--mlock' recommended to lock model into RAM for consistent performance." in verbose_output

def test_wizard_handles_invalid_model_path():
    with patch('llama_tune.wizard.interactive_wizard.get_system_profile') as mock_get_system_profile, \
         patch('llama_tune.wizard.interactive_wizard.get_model_profile') as mock_get_model_profile, \
         patch('llama_tune.wizard.interactive_wizard.run_feasibility_check') as mock_run_feasibility_check, \
         patch('questionary.select') as mock_questionary_select, \
         patch('questionary.text') as mock_questionary_text:

        # Mock system profile
        mock_get_system_profile.return_value = SystemProfile(
            cpu_cores=8,
            total_ram_gb=32.0,
            gpus=[],
            numa_detected=False,
            blas_backend="cuBLAS"
        )

        # Mock feasibility check to always pass
        mock_run_feasibility_check.return_value = None

        # Mock questionary prompts
        mock_questionary_select.return_value.ask.return_value = "Conversational Chatbot"

        # Create a list to track calls
        call_values = [
            "4096", # ctx_size
            "/invalid/path/to/model.gguf", # First model_path attempt
            "/path/to/model.gguf" # Second model_path attempt (valid)
        ]

        # Create a function that tracks calls and returns values
        def mock_ask():
            if call_values:
                return call_values.pop(0)
            else:
                raise Exception("No more mock values available")

        mock_questionary_text.return_value.ask = mock_ask

        # Mock get_model_profile to raise FeasibilityError for the invalid path
        mock_get_model_profile.side_effect = [
            FeasibilityError("Invalid model path for testing"), # Simulate FeasibilityError for invalid path
            ModelProfile(file_path="/path/to/model.gguf", architecture="llama", layer_count=32, quantization_type="Q4_0")
        ]

        wizard = InteractiveWizard()
        with patch('typer.echo') as mock_typer_echo:
            wizard.start()
            # Assert that an error message was echoed for the invalid path
            mock_typer_echo.assert_any_call("Error: Invalid model path for testing. Please provide a valid GGUF model path.")

        optimal_config = wizard.get_optimal_configuration()
        assert optimal_config is not None
        assert optimal_config.model_profile.file_path == "/path/to/model.gguf"