import pytest
from unittest.mock import patch, MagicMock
from llama_tune.analyzer.analyzer import get_blas_backend
from llama_tune.core.data_models import SystemProfile, OptimalConfiguration, ModelProfile, BenchmarkResult
from llama_tune.reporting.output_generator import generate_output

def test_get_blas_backend_integration():
    # Mock the subprocess.run function to return a mocked output
    with patch('subprocess.run') as mock_subprocess_run:
        mock_subprocess_run.return_value = MagicMock(
            stdout="""
            llama.cpp build info:
            BLAS = 1 (cuBLAS)
            """,
            stderr="",
            returncode=0
        )
        
        # Call the get_blas_backend function
        blas_backend = get_blas_backend()
        
        # Assert that the correct BLAS backend is detected
        assert blas_backend == "cuBLAS"

def test_output_generator_integration():
    # Create a mocked SystemProfile object with a high-performance BLAS backend
    system_profile = SystemProfile(
        cpu_cores=8,
        total_ram_gb=32.0,
        gpus=[],
        numa_detected=False,
        blas_backend="cuBLAS"
    )
    
    # Call the Output Generator component
    model_profile = ModelProfile(file_path="", architecture="", layer_count=0, quantization_type="")
    benchmark_result = BenchmarkResult(n_gpu_layers=0, prompt_speed_tps=0.0, generation_speed_tps=0.0, batch_size=None, parallel_level=None)
    output = generate_output(OptimalConfiguration(system_profile=system_profile, model_profile=model_profile, best_benchmark_result=benchmark_result, generated_command="", notes=[]), "verbose")
    
    # Assert that the correct flag is added to the final command
    assert "--use-cublas" in output