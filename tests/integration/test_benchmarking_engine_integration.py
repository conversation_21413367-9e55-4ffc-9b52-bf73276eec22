import pytest
from unittest.mock import MagicMock, patch
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.core.data_models import ModelProfile, SystemProfile, BenchmarkResult, OptimalConfiguration

@pytest.fixture
def mock_model_profile():
    return ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=30,
        quantization_type="Q4_0"
    )

@pytest.fixture
def mock_system_profile():
    return SystemProfile(
        cpu_cores=8,
        total_ram_gb=16.0,
        gpus=[],
        numa_detected=False,
        blas_backend="BLIS"
    )

@pytest.fixture
def benchmarking_engine():
    return BenchmarkingEngine()

def test_run_benchmark_orchestrates_phases(benchmarking_engine, mock_model_profile, mock_system_profile):
    with patch('llama_tune.benchmarker.benchmarking_engine.get_system_profile', return_value=mock_system_profile) as mock_get_system_profile, \
         patch('llama_tune.benchmarker.benchmarking_engine.get_model_profile', return_value=mock_model_profile) as mock_get_model_profile, \
         patch.object(benchmarking_engine, '_run_gpu_offload_benchmark') as mock_gpu_offload_benchmark:
        
        # Mock the return value of the GPU offload benchmark
        mock_gpu_offload_benchmark.return_value = BenchmarkResult(
            n_gpu_layers=10,
            prompt_speed_tps=50.0,
            generation_speed_tps=20.0,
            batch_size=None,
            parallel_level=None
        )

        model_path = "/path/to/model.gguf"
        ctx_size = 512
        optimal_config = benchmarking_engine.run_benchmark(model_path, ctx_size, "default", None, None)

        # Assert that analyzer methods were called
        mock_get_system_profile.assert_called_once()
        mock_get_model_profile.assert_called_once_with(model_path)

        # Assert that the GPU offload benchmark method was called with correct arguments
        mock_gpu_offload_benchmark.assert_called_once_with(mock_model_profile, ctx_size)

        # Assert the final OptimalConfiguration structure
        assert isinstance(optimal_config, OptimalConfiguration)
        assert optimal_config.system_profile == mock_system_profile
        assert optimal_config.model_profile == mock_model_profile
        assert optimal_config.best_benchmark_result.n_gpu_layers == 10
        assert optimal_config.ctx_size == ctx_size
        assert "GPU offload benchmark completed." in optimal_config.notes