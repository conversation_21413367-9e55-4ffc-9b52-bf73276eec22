import pytest
from unittest.mock import MagicMock, patch
import subprocess
import async<PERSON>
import httpx
from llama_tune.benchmarker.benchmarking_engine import BenchmarkingEngine
from llama_tune.core.data_models import ModelProfile

@pytest.fixture
def mock_model_profile():
    return ModelProfile(
        file_path="/path/to/model.gguf",
        architecture="llama",
        layer_count=30,
        quantization_type="q4_0"
    )

@pytest.fixture
def benchmarking_engine():
    return BenchmarkingEngine()

@pytest.mark.asyncio
@patch('llama_tune.benchmarker.benchmarking_engine.subprocess.Popen')
@patch('llama_tune.benchmarker.benchmarking_engine.time.sleep')
@patch('llama_tune.benchmarker.benchmarking_engine.httpx.AsyncClient.post')
async def test_llama_server_lifecycle_in_throughput_benchmark(mock_httpx_post, mock_sleep, mock_popen, benchmarking_engine, mock_model_profile):
    # Mock subprocess.Popen for llama-server
    mock_process = MagicMock()
    mock_process.stdout.readline.side_effect = ["HTTP server listening\n"] * 200 # Provide enough lines for all iterations
    mock_process.poll.return_value = None # Simulate server running
    mock_process.stderr.read.return_value = "" # No stderr output
    mock_popen.return_value = mock_process

    # Mock httpx.AsyncClient.post for client requests
    mock_response = MagicMock()
    mock_response.json.return_value = {"content": "mocked response content"}
    mock_response.raise_for_status.return_value = None
    mock_httpx_post.return_value.__aenter__.return_value = mock_response

    # Mock _simulate_concurrent_clients to return a dummy throughput
    with patch('llama_tune.benchmarker.benchmarking_engine.BenchmarkingEngine._simulate_concurrent_clients', side_effect=[100.0] * 200) as mock_simulate:
        # Call the throughput benchmark method
        result = await benchmarking_engine._run_throughput_benchmark(mock_model_profile, 2048, 10)

        # Assertions
        mock_popen.assert_called() # Check if llama-server was attempted to be started
        mock_sleep.assert_called() # Check if sleep was called for server startup
        mock_process.terminate.assert_called() # Check if server was terminated
        mock_process.wait.assert_called() # Check if wait was called
        assert result is not None
        assert result.generation_speed_tps == 100.0 # From mocked simulate_concurrent_clients
